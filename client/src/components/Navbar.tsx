import { useState, useEffect } from "react";
import { useLocation, Link } from "wouter";
import { motion } from "framer-motion";
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet";
import ailexIcon from "@assets/webp/ailex-icon.webp";

type NavbarProps = {
  currentState?: "TX" | "FL" | "NY";
};

export default function Navbar({ currentState }: NavbarProps) {
  const [isScrolled, setIsScrolled] = useState(false);
  const [location, setLocation] = useLocation();

  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 32);
    };

    window.addEventListener("scroll", handleScroll);
    return () => {
      window.removeEventListener("scroll", handleScroll);
    };
  }, []);

  // Function to navigate to homepage or scroll to top
  const handleLogoClick = (e: React.MouseEvent) => {
    e.preventDefault();

    // Small visual feedback animation
    const target = e.currentTarget;
    target.classList.add("scale-105");
    setTimeout(() => {
      target.classList.remove("scale-105");
    }, 200);

    if (location === "/") {
      // Already on homepage, just scroll to top
      window.scrollTo({
        top: 0,
        behavior: "smooth",
      });
    } else {
      // Navigate to homepage
      setLocation("/");
    }
  };

  // Function to navigate to homepage and scroll to section
  const navigateToSection = (sectionId: string) => {
    if (location === "/") {
      // Already on homepage, just scroll
      document.getElementById(sectionId)?.scrollIntoView({
        behavior: "smooth",
        block: "start",
      });
    } else {
      // Navigate to homepage first, then scroll
      setLocation("/");
      setTimeout(() => {
        document.getElementById(sectionId)?.scrollIntoView({
          behavior: "smooth",
          block: "start",
        });
      }, 100);
    }
  };

  return (
    <header
      className={`fixed w-full z-50 top-0 h-16 flex items-center transition-default ${
        isScrolled ? "bg-navy shadow-md" : ""
      }`}
    >
      <div className="container-full w-full flex items-center justify-between min-h-[64px]">
        <a
          href="#"
          onClick={handleLogoClick}
          className="flex items-center cursor-pointer transition-all duration-200 ease-out"
        >
          <motion.img
            src={ailexIcon}
            alt="AiLex Icon"
            className="h-8 w-auto"
            loading="lazy"
            decoding="async"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ duration: 0.5 }}
          />
          <span
            className={`ml-3 text-lg font-bold tracking-wide ${isScrolled ? "text-white" : "text-navy"}`}
          >
            AiLex
          </span>
          {currentState && (
            <span className="ml-2 code text-xs bg-[#B8FF5C] rounded-full px-2 py-1 text-navy">
              {currentState}
            </span>
          )}
        </a>

        <div className="hidden md:flex items-center space-x-8">
          <button
            onClick={() => navigateToSection("features")}
            className={`relative text-sm ${isScrolled ? "text-gray-300" : "text-gray-600"} transition-all duration-300 ease-out hover:text-[#1EAEDB] group`}
          >
            Features
            <span className="absolute bottom-[-6px] left-0 w-0 h-0.5 bg-[#1EAEDB] transition-all duration-300 ease-out group-hover:w-full"></span>
          </button>
          <button
            onClick={() => navigateToSection("pricing")}
            className={`relative text-sm ${isScrolled ? "text-gray-300" : "text-gray-600"} transition-all duration-300 ease-out hover:text-[#1EAEDB] group`}
          >
            Pricing
            <span className="absolute bottom-[-6px] left-0 w-0 h-0.5 bg-[#1EAEDB] transition-all duration-300 ease-out group-hover:w-full"></span>
          </button>
          <Link
            href="/blog"
            className={`relative text-sm ${isScrolled ? "text-gray-300" : "text-gray-600"} transition-all duration-300 ease-out hover:text-[#1EAEDB] group`}
          >
            Blog
            <span className="absolute bottom-[-6px] left-0 w-0 h-0.5 bg-[#1EAEDB] transition-all duration-300 ease-out group-hover:w-full"></span>
          </Link>
          <button
            onClick={() => navigateToSection("faq")}
            className={`relative text-sm ${isScrolled ? "text-gray-300" : "text-gray-600"} transition-all duration-300 ease-out hover:text-[#1EAEDB] group`}
          >
            FAQ
            <span className="absolute bottom-[-6px] left-0 w-0 h-0.5 bg-[#1EAEDB] transition-all duration-300 ease-out group-hover:w-full"></span>
          </button>
          <button
            onClick={() => navigateToSection("contact")}
            className={`relative text-sm ${isScrolled ? "text-gray-300" : "text-gray-600"} transition-all duration-300 ease-out hover:text-[#1EAEDB] group`}
          >
            Contact
            <span className="absolute bottom-[-6px] left-0 w-0 h-0.5 bg-[#1EAEDB] transition-all duration-300 ease-out group-hover:w-full"></span>
          </button>
        </div>

        <div className="flex items-center">
          {/* Desktop Navigation Buttons - Hidden on mobile */}
          <div className="hidden md:flex items-center space-x-3">
            <a
              href="/login"
              className="text-sm px-6 py-3 border border-[#1EAEDB] text-[#1EAEDB] rounded-xl hover:bg-[#1EAEDB] hover:text-white transition-all duration-300 ease-out font-medium"
            >
              Login
            </a>
            <a href="/login" className="btn-primary text-sm">
              Start free trial
            </a>
          </div>

          {/* Mobile Navigation - Hamburger Menu */}
          <Sheet>
            <SheetTrigger asChild>
              <button
                className="md:hidden mobile-touch-target p-2 -mr-2 text-gray-600 hover:text-gray-800 transition-colors duration-200 focus:outline-none focus:ring-2 focus:ring-primary focus:ring-offset-2 rounded-lg"
                aria-label="Open navigation menu"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  className={`h-6 w-6 ${isScrolled ? "text-white hover:text-gray-200" : "text-gray-600 hover:text-gray-800"}`}
                  fill="none"
                  viewBox="0 0 24 24"
                  stroke="currentColor"
                >
                  <path
                    strokeLinecap="round"
                    strokeLinejoin="round"
                    strokeWidth={2}
                    d="M4 6h16M4 12h16M4 18h16"
                  />
                </svg>
              </button>
            </SheetTrigger>
            <SheetContent side="right" className="w-[320px] sm:w-[280px]">
              <div className="flex flex-col h-full">
                {/* Mobile Menu Header */}
                <div className="flex items-center justify-between mb-8 mt-2">
                  <div className="flex items-center">
                    <img
                      src={ailexIcon}
                      alt="AiLex Icon"
                      className="h-6 w-auto mr-2"
                    />
                    <span className="text-lg font-bold text-navy">AiLex</span>
                  </div>
                </div>

                {/* Navigation Links */}
                <nav className="flex flex-col space-y-1 flex-1">
                  <a
                    href="#"
                    onClick={handleLogoClick}
                    className="mobile-nav-item py-3 px-3 text-base font-medium text-gray-700 hover:text-primary hover:bg-gray-50 rounded-lg transition-all duration-200 ease-out"
                  >
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5 mr-3 text-primary"
                      viewBox="0 0 20 20"
                      fill="currentColor"
                    >
                      <path
                        fillRule="evenodd"
                        d="M14.707 12.707a1 1 0 01-1.414 0L10 9.414l-3.293 3.293a1 1 0 01-1.414-1.414l4-4a1 1 0 011.414 0l4 4a1 1 0 010 1.414z"
                        clipRule="evenodd"
                      />
                    </svg>
                    Back to Top
                  </a>

                  <button
                    onClick={() => navigateToSection("features")}
                    className="mobile-nav-item py-3 px-3 text-base font-medium text-gray-700 hover:text-primary hover:bg-gray-50 rounded-lg transition-all duration-200 ease-out text-left w-full"
                  >
                    Features
                  </button>

                  <button
                    onClick={() => navigateToSection("pricing")}
                    className="mobile-nav-item py-3 px-3 text-base font-medium text-gray-700 hover:text-primary hover:bg-gray-50 rounded-lg transition-all duration-200 ease-out text-left w-full"
                  >
                    Pricing
                  </button>

                  <Link
                    href="/blog"
                    className="mobile-nav-item py-3 px-3 text-base font-medium text-gray-700 hover:text-primary hover:bg-gray-50 rounded-lg transition-all duration-200 ease-out"
                  >
                    Blog
                  </Link>

                  <button
                    onClick={() => navigateToSection("faq")}
                    className="mobile-nav-item py-3 px-3 text-base font-medium text-gray-700 hover:text-primary hover:bg-gray-50 rounded-lg transition-all duration-200 ease-out text-left w-full"
                  >
                    FAQ
                  </button>

                  <button
                    onClick={() => navigateToSection("contact")}
                    className="mobile-nav-item py-3 px-3 text-base font-medium text-gray-700 hover:text-primary hover:bg-gray-50 rounded-lg transition-all duration-200 ease-out text-left w-full"
                  >
                    Contact
                  </button>
                </nav>

                {/* Mobile CTA Buttons */}
                <div className="border-t border-gray-200 pt-6 mt-6 space-y-3">
                  <a
                    href="/login"
                    className="block w-full text-center py-3 px-4 text-base font-medium border border-[#1EAEDB] text-[#1EAEDB] rounded-xl hover:bg-[#1EAEDB] hover:text-white transition-all duration-300 ease-out"
                  >
                    Login
                  </a>
                  <a
                    href="/login"
                    className="block w-full text-center py-3 px-4 text-base font-medium bg-primary hover:bg-[#0C1C2D] hover:border hover:border-white text-white rounded-xl transition-all duration-300 ease-out border border-transparent"
                  >
                    Start free trial
                  </a>
                </div>

                {/* State selector in mobile menu */}
                {currentState && (
                  <div className="border-t border-gray-200 pt-4 mt-4">
                    <p className="text-sm font-medium mb-3 text-gray-600">
                      Switch State:
                    </p>
                    <div className="grid grid-cols-1 gap-2">
                      <Link
                        href="/tx"
                        className={`code text-sm px-4 py-2 rounded-lg border text-center transition-all duration-200 ${
                          currentState === "TX"
                            ? "bg-primary text-white border-primary"
                            : "border-gray-300 text-gray-700 hover:border-primary hover:text-primary"
                        }`}
                      >
                        Texas
                      </Link>
                      <Link
                        href="/fl"
                        className={`code text-sm px-4 py-2 rounded-lg border text-center transition-all duration-200 ${
                          currentState === "FL"
                            ? "bg-primary text-white border-primary"
                            : "border-gray-300 text-gray-700 hover:border-primary hover:text-primary"
                        }`}
                      >
                        Florida
                      </Link>
                      <Link
                        href="/ny"
                        className={`code text-sm px-4 py-2 rounded-lg border text-center transition-all duration-200 ${
                          currentState === "NY"
                            ? "bg-primary text-white border-primary"
                            : "border-gray-300 text-gray-700 hover:border-primary hover:text-primary"
                        }`}
                      >
                        New York
                      </Link>
                    </div>
                  </div>
                )}
              </div>
            </SheetContent>
          </Sheet>
        </div>
      </div>
    </header>
  );
}
